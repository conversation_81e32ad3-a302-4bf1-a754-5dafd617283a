{"hash": "53433488", "browserHash": "11d12231", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "dceac358", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "a323a4ba", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "9538bcdc", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "0b77af2b", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f028c6a5", "needsInterop": true}, "@modelcontextprotocol/sdk/client/index.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/index.js", "file": "@modelcontextprotocol_sdk_client_index__js.js", "fileHash": "80394800", "needsInterop": false}, "@modelcontextprotocol/sdk/client/sse.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/sse.js", "file": "@modelcontextprotocol_sdk_client_sse__js.js", "fileHash": "eba3b895", "needsInterop": false}, "@modelcontextprotocol/sdk/client/streamableHttp.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js", "file": "@modelcontextprotocol_sdk_client_streamableHttp__js.js", "fileHash": "348906bd", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "81a7f795", "needsInterop": false}, "@vapi-ai/web": {"src": "../../@vapi-ai/web/dist/vapi.js", "file": "@vapi-ai_web.js", "fileHash": "48741443", "needsInterop": true}, "jose": {"src": "../../jose/dist/webapi/index.js", "file": "jose.js", "fileHash": "e90e66e2", "needsInterop": false}, "leaflet": {"src": "../../leaflet/dist/leaflet-src.js", "file": "leaflet.js", "fileHash": "abc52b29", "needsInterop": true}, "lodash": {"src": "../../lodash/lodash.js", "file": "lodash.js", "fileHash": "5345bc8c", "needsInterop": true}, "papaparse": {"src": "../../papaparse/papaparse.min.js", "file": "papaparse.js", "fileHash": "c0c4abc1", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0b03abc7", "needsInterop": true}, "react-dom/server": {"src": "../../react-dom/server.browser.js", "file": "react-dom_server.js", "fileHash": "64960f41", "needsInterop": true}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "a44a1d39", "needsInterop": false}, "react-icons/md": {"src": "../../react-icons/md/index.mjs", "file": "react-icons_md.js", "fileHash": "5b3f8682", "needsInterop": false}, "react-markdown": {"src": "../../react-markdown/index.js", "file": "react-markdown.js", "fileHash": "a46a3b8d", "needsInterop": false}, "react-toastify": {"src": "../../react-toastify/dist/index.mjs", "file": "react-toastify.js", "fileHash": "a6f75f68", "needsInterop": false}, "rehype-raw": {"src": "../../rehype-raw/index.js", "file": "rehype-raw.js", "fileHash": "fae5ec6e", "needsInterop": false}, "rehype-sanitize": {"src": "../../rehype-sanitize/index.js", "file": "rehype-sanitize.js", "fileHash": "45df8503", "needsInterop": false}, "remark-gfm": {"src": "../../remark-gfm/index.js", "file": "remark-gfm.js", "fileHash": "98c87a6a", "needsInterop": false}, "uuid": {"src": "../../uuid/dist/esm-browser/index.js", "file": "uuid.js", "fileHash": "4af3750e", "needsInterop": false}, "xlsx": {"src": "../../xlsx/xlsx.mjs", "file": "xlsx.js", "fileHash": "22ba985d", "needsInterop": false}}, "chunks": {"browser-2H53BKCB": {"file": "browser-2H53BKCB.js"}, "browser-L3WHH2Q2": {"file": "browser-L3WHH2Q2.js"}, "chunk-CUTKQHYX": {"file": "chunk-CUTKQHYX.js"}, "chunk-FYR2ONTC": {"file": "chunk-FYR2ONTC.js"}, "chunk-ZKFSI6YS": {"file": "chunk-ZKFSI6YS.js"}, "chunk-WLB7R6ZN": {"file": "chunk-WLB7R6ZN.js"}, "chunk-EFHT7PV7": {"file": "chunk-EFHT7PV7.js"}, "chunk-3TH3G7JX": {"file": "chunk-3TH3G7JX.js"}, "chunk-A5ED6EHL": {"file": "chunk-A5ED6EHL.js"}, "chunk-Q72EVS5P": {"file": "chunk-Q72EVS5P.js"}, "chunk-73YGM6QR": {"file": "chunk-73YGM6QR.js"}, "chunk-2N3A5BUM": {"file": "chunk-2N3A5BUM.js"}, "chunk-B6J4VDVC": {"file": "chunk-B6J4VDVC.js"}, "chunk-SJSTY3YX": {"file": "chunk-SJSTY3YX.js"}, "chunk-C3M7BXFS": {"file": "chunk-C3M7BXFS.js"}}}